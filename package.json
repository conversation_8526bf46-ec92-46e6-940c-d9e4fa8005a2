{"name": "hls-cors-proxy", "version": "1.0.0", "description": "A simple CORS proxy server for HLS video streams", "main": "proxy-server.js", "scripts": {"start": "node proxy-server.js", "dev": "nodemon proxy-server.js"}, "dependencies": {"express": "^4.18.2", "http-proxy-middleware": "^2.0.6", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["cors", "proxy", "hls", "video", "streaming"], "author": "", "license": "MIT"}