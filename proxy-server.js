const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Enable CORS for all routes
app.use(cors({
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Range'],
    credentials: false
}));

// Proxy middleware for HLS streams
const proxyOptions = {
    target: 'https://vid24.strp2p.com',
    changeOrigin: true,
    pathRewrite: {
        '^/proxy': '', // remove /proxy from the beginning of the path
    },
    onProxyReq: (proxyReq, req, res) => {
        // Remove problematic headers
        proxyReq.removeHeader('origin');
        proxyReq.removeHeader('referer');
        
        // Set headers that might help
        proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        proxyReq.setHeader('Accept', '*/*');
        proxyReq.setHeader('Accept-Language', 'en-US,en;q=0.9');
        proxyReq.setHeader('Cache-Control', 'no-cache');
        proxyReq.setHeader('Pragma', 'no-cache');
    },
    onProxyRes: (proxyRes, req, res) => {
        // Add CORS headers to the response
        proxyRes.headers['Access-Control-Allow-Origin'] = '*';
        proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
        proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, Range';
        proxyRes.headers['Access-Control-Expose-Headers'] = 'Content-Length, Content-Range';
        
        // Remove problematic headers
        delete proxyRes.headers['x-frame-options'];
        delete proxyRes.headers['content-security-policy'];
    },
    onError: (err, req, res) => {
        console.error('Proxy error:', err);
        res.status(500).json({ error: 'Proxy error', details: err.message });
    }
};

// Use the proxy middleware
app.use('/proxy', createProxyMiddleware(proxyOptions));

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'OK', message: 'CORS Proxy Server is running' });
});

// Start the server
app.listen(PORT, () => {
    console.log(`CORS Proxy Server running on http://localhost:${PORT}`);
    console.log(`Use http://localhost:${PORT}/proxy/[original-url-path] to proxy requests`);
});
