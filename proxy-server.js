const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');
const { URL } = require('url');

const app = express();
const PORT = 3001;

// Enable CORS for all routes
app.use(cors({
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Range'],
    credentials: false
}));

// Dynamic proxy middleware that can handle multiple domains
const createDynamicProxy = () => {
    return (req, res, next) => {
        // Extract the target URL from the request
        const targetUrl = req.query.url;

        if (!targetUrl) {
            return res.status(400).json({ error: 'Missing url parameter' });
        }

        try {
            const parsedUrl = new URL(targetUrl);
            const target = `${parsedUrl.protocol}//${parsedUrl.host}`;

            console.log(`Proxying request to: ${targetUrl}`);

            // Create proxy middleware for this specific request
            const proxyMiddleware = createProxyMiddleware({
                target: target,
                changeOrigin: true,
                pathRewrite: (path, req) => {
                    return parsedUrl.pathname + parsedUrl.search;
                },
                onProxyReq: (proxyReq, req, res) => {
                    // Remove problematic headers
                    proxyReq.removeHeader('origin');
                    proxyReq.removeHeader('referer');
                    proxyReq.removeHeader('host');

                    // Set headers that might help
                    proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
                    proxyReq.setHeader('Accept', '*/*');
                    proxyReq.setHeader('Accept-Language', 'en-US,en;q=0.9');
                    proxyReq.setHeader('Accept-Encoding', 'gzip, deflate, br');
                    proxyReq.setHeader('Cache-Control', 'no-cache');
                    proxyReq.setHeader('Pragma', 'no-cache');
                    proxyReq.setHeader('Sec-Fetch-Dest', 'empty');
                    proxyReq.setHeader('Sec-Fetch-Mode', 'cors');
                    proxyReq.setHeader('Sec-Fetch-Site', 'cross-site');
                },
                onProxyRes: (proxyRes, req, res) => {
                    // Add CORS headers to the response
                    proxyRes.headers['Access-Control-Allow-Origin'] = '*';
                    proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
                    proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, Range';
                    proxyRes.headers['Access-Control-Expose-Headers'] = 'Content-Length, Content-Range';

                    // Remove problematic headers
                    delete proxyRes.headers['x-frame-options'];
                    delete proxyRes.headers['content-security-policy'];
                    delete proxyRes.headers['x-content-type-options'];

                    console.log(`Response from ${target}: ${proxyRes.statusCode}`);
                },
                onError: (err, req, res) => {
                    console.error('Proxy error:', err.message);
                    if (!res.headersSent) {
                        res.status(500).json({ error: 'Proxy error', details: err.message });
                    }
                }
            });

            // Execute the proxy middleware
            proxyMiddleware(req, res, next);

        } catch (error) {
            console.error('URL parsing error:', error.message);
            res.status(400).json({ error: 'Invalid URL', details: error.message });
        }
    };
};

// Use the dynamic proxy middleware
app.use('/proxy', createDynamicProxy());

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'OK', message: 'CORS Proxy Server is running' });
});

// Start the server
app.listen(PORT, () => {
    console.log(`CORS Proxy Server running on http://localhost:${PORT}`);
    console.log(`Use http://localhost:${PORT}/proxy/[original-url-path] to proxy requests`);
});
