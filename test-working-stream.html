<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Working HLS Stream</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎬</text></svg>">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
            background-color: #111827;
        }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen">
    <div class="w-full max-w-4xl p-4 md:p-6 bg-gray-900 rounded-2xl shadow-2xl">
        <h1 class="text-2xl md:text-3xl font-bold text-white mb-4 text-center">Test HLS Stream</h1>
        
        <div class="aspect-w-16 aspect-h-9 bg-black rounded-lg overflow-hidden">
            <video id="videoPlayer" class="w-full h-full" controls autoplay muted></video>
        </div>

        <p id="status" class="text-center text-gray-400 mt-4"></p>
        
        <div class="mt-4 space-y-2">
            <button onclick="testStream1()" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded">
                Test Stream 1: Big Buck Bunny
            </button>
            <button onclick="testStream2()" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded">
                Test Stream 2: Sintel
            </button>
            <button onclick="testOriginalStream()" class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded">
                Test Original Stream (through proxy)
            </button>
        </div>
    </div>

    <script>
        const video = document.getElementById('videoPlayer');
        const statusElement = document.getElementById('status');
        let hls = null;

        // Custom loader to proxy requests
        class ProxyLoader extends Hls.DefaultConfig.loader {
            load(context, config, callbacks) {
                // Only proxy if it's the original problematic stream
                if (context.url.includes('vid24.strp2p.com')) {
                    const originalUrl = context.url;
                    context.url = `http://localhost:3001/proxy?url=${encodeURIComponent(originalUrl)}`;
                    console.log(`Proxying: ${originalUrl} -> ${context.url}`);
                }
                super.load(context, config, callbacks);
            }
        }

        function loadStream(streamUrl, description) {
            statusElement.textContent = `Loading ${description}...`;
            
            if (hls) {
                hls.destroy();
            }

            if (Hls.isSupported()) {
                hls = new Hls({
                    loader: ProxyLoader,
                    debug: true
                });

                hls.loadSource(streamUrl);
                hls.attachMedia(video);

                hls.on(Hls.Events.MANIFEST_PARSED, () => {
                    console.log('Manifest parsed successfully');
                    statusElement.textContent = `${description} loaded successfully!`;
                    video.play().catch(error => {
                        console.error('Autoplay prevented:', error);
                        statusElement.textContent = 'Click play button to start video.';
                    });
                });

                hls.on(Hls.Events.ERROR, (event, data) => {
                    console.error('HLS error:', data);
                    if (data.fatal) {
                        statusElement.textContent = `Error loading ${description}: ${data.details}`;
                    }
                });

            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                video.src = streamUrl;
                statusElement.textContent = `${description} loaded with native HLS support.`;
            } else {
                statusElement.textContent = 'HLS not supported in this browser.';
            }
        }

        function testStream1() {
            // Big Buck Bunny - a reliable test stream
            loadStream('https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8', 'Big Buck Bunny');
        }

        function testStream2() {
            // Sintel - another reliable test stream
            loadStream('https://bitdash-a.akamaihd.net/content/sintel/hls/playlist.m3u8', 'Sintel');
        }

        function testOriginalStream() {
            // Your original stream through proxy
            const originalUrl = 'https://vid24.strp2p.com/hls/eZqh3HvcnrH9rrxAYr7Xlg/vz/b6ikxf9x/8v9bn3/tt/index-f1-v1-a1.m3u8?v=1751082628';
            loadStream(originalUrl, 'Original Stream (Proxied)');
        }

        // Auto-load first test stream
        window.addEventListener('load', () => {
            setTimeout(testStream1, 500);
        });
    </script>
</body>
</html>
