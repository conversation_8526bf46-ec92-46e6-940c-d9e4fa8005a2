<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom HLS Video Player</title>
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- HLS.js library for playing HLS streams -->
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <style>
        /* Custom font and body background */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
            background-color: #111827; /* Dark gray background */
        }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen">

    <div class="w-full max-w-4xl p-4 md:p-6 bg-gray-900 rounded-2xl shadow-2xl">
        <h1 class="text-2xl md:text-3xl font-bold text-white mb-4 text-center">Video Player</h1>
        
        <!-- The video element where the stream will be displayed -->
        <div class="aspect-w-16 aspect-h-9 bg-black rounded-lg overflow-hidden">
            <video id="videoPlayer" class="w-full h-full" controls autoplay muted></video>
        </div>

        <p id="status" class="text-center text-gray-400 mt-4"></p>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const video = document.getElementById('videoPlayer');
            const statusElement = document.getElementById('status');
            // User's M3U8 stream URL
            const videoSrc = 'https://vid24.strp2p.com/hls/eZqh3HvcnrH9rrxAYr7Xlg/vz/b6ikxf9x/8v9bn3/tt/index-f1-v1-a1.m3u8?v=1751082628';

            // Check if HLS is supported by the browser
            if (Hls.isSupported()) {
                console.log("HLS.js is supported. Initializing player.");
                statusElement.textContent = 'Player load hocche...';
                
                const hls = new Hls({
                    // Increase timeout and retry attempts for manifest loading
                    manifestLoadingTimeOut: 20000,
                    manifestLoadingMaxRetry: 5,
                    manifestLoadingRetryDelay: 1000,
                    // Explicitly set withCredentials to false which can help with some CORS issues
                    xhrSetup: function(xhr, url) {
                        xhr.withCredentials = false;
                    }
                });

                // Load the video source
                hls.loadSource(videoSrc);
                // Attach HLS.js to the video element
                hls.attachMedia(video);

                // Event listener for when the manifest is parsed
                hls.on(Hls.Events.MANIFEST_PARSED, () => {
                    console.log('Manifest parsed. Playing video.');
                    statusElement.textContent = 'Video cholche.';
                    // Attempt to play the video. Muted autoplay is generally allowed.
                    video.play().catch(error => {
                        console.error('Autoplay was prevented:', error);
                        statusElement.textContent = 'Play button-e click korun.';
                    });
                });

                // Error handling
                hls.on(Hls.Events.ERROR, (event, data) => {
                    console.error("HLS.js error:", data); // Log the full error data
                    if (data.fatal) {
                        switch (data.type) {
                            case Hls.ErrorTypes.NETWORK_ERROR:
                                // This error is often due to CORS issues or network problems.
                                if (data.details === 'manifestLoadError') {
                                    statusElement.textContent = 'Video manifest load kora jayni. CORS policy or network issue hote pare.';
                                    console.error('Manifest load error. This could be a CORS issue. Check the server configuration.');
                                } else {
                                    console.error('Fatal network error occurred, trying to recover:', data);
                                    statusElement.textContent = 'Network error. Punoray cheshta kora hocche...';
                                    hls.startLoad(); // try to recover
                                }
                                break;
                            case Hls.ErrorTypes.MEDIA_ERROR:
                                console.error('Fatal media error occurred, trying to recover:', data);
                                statusElement.textContent = 'Media error. Player recover korar cheshta korche...';
                                hls.recoverMediaError();
                                break;
                            default:
                                // Cannot recover
                                console.error('Fatal error, cannot recover:', data);
                                hls.destroy();
                                statusElement.textContent = 'Ekti fatal error hoyeche. Player bondho hoye geche.';
                                break;
                        }
                    }
                });

            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                // For browsers like Safari that have native HLS support
                console.log("Native HLS support found.");
                video.src = videoSrc;
                video.addEventListener('loadedmetadata', () => {
                    video.play().catch(error => {
                        console.error('Autoplay was prevented:', error);
                        statusElement.textContent = 'Play button-e click korun.';
                    });
                });
                 video.addEventListener('error', (e) => {
                    statusElement.textContent = 'Video load kora jayni. Link-ti expire hoye geche or network-e shomossha.';
                    console.error('Native player error:', e);
                });
            } else {
                // HLS is not supported
                console.error("Sorry, your browser does not support HLS video playback.");
                statusElement.textContent = 'Apnar browser HLS video support kore na.';
            }
        });
    </script>

</body>
</html>
