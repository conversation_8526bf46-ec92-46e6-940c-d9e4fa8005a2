<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom HLS Video Player</title>
    <!-- Add favicon to prevent 404 error -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎬</text></svg>">
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- HLS.js library for playing HLS streams -->
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <style>
        /* Custom font and body background */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
            background-color: #111827; /* Dark gray background */
        }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen">

    <div class="w-full max-w-4xl p-4 md:p-6 bg-gray-900 rounded-2xl shadow-2xl">
        <h1 class="text-2xl md:text-3xl font-bold text-white mb-4 text-center">Video Player</h1>
        
        <!-- The video element where the stream will be displayed -->
        <div class="aspect-w-16 aspect-h-9 bg-black rounded-lg overflow-hidden">
            <video id="videoPlayer" class="w-full h-full" controls autoplay muted></video>
        </div>

        <p id="status" class="text-center text-gray-400 mt-4"></p>

        <div class="mt-4 p-4 bg-gray-800 rounded-lg">
            <h3 class="text-white font-semibold mb-2">Debug Info:</h3>
            <div id="debugInfo" class="text-sm text-gray-300 space-y-1"></div>
            <button onclick="testProxyHealth()" class="mt-2 bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm">
                Test Proxy Server
            </button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const video = document.getElementById('videoPlayer');
            const statusElement = document.getElementById('status');
            const debugInfo = document.getElementById('debugInfo');

            // Original M3U8 stream URL
            const originalVideoSrc = 'https://vid24.strp2p.com/hls/eZqh3HvcnrH9rrxAYr7Xlg/vz/b6ikxf9x/8v9bn3/tt/index-f1-v1-a1.m3u8?v=**********';

            // CORS proxy options (try these in order)
            const corsProxies = [
                `http://localhost:3001/proxy?url=${encodeURIComponent(originalVideoSrc)}`, // Local dynamic proxy server
                originalVideoSrc, // Try direct access
                `https://cors-anywhere.herokuapp.com/${originalVideoSrc}`,
                `https://api.allorigins.win/raw?url=${encodeURIComponent(originalVideoSrc)}`,
                `https://corsproxy.io/?${encodeURIComponent(originalVideoSrc)}`
            ];

            let currentProxyIndex = 0;
            let hls = null;

            // Debug logging function
            function addDebugInfo(message) {
                const div = document.createElement('div');
                div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
                debugInfo.appendChild(div);
                console.log(message);
            }

            // Test proxy server health
            async function testProxyHealth() {
                try {
                    const response = await fetch('http://localhost:3001/health');
                    if (response.ok) {
                        const data = await response.json();
                        addDebugInfo(`✅ Proxy server is healthy: ${data.message}`);
                    } else {
                        addDebugInfo(`❌ Proxy server error: ${response.status}`);
                    }
                } catch (error) {
                    addDebugInfo(`❌ Cannot connect to proxy server: ${error.message}`);
                }
            }

            // Make testProxyHealth available globally
            window.testProxyHealth = testProxyHealth;

            // Function to try loading video with different proxy URLs
            function tryLoadVideo() {
                if (currentProxyIndex >= corsProxies.length) {
                    statusElement.textContent = 'Shob proxy try kora hoyeche. Video load kora jayni.';
                    console.error('All proxy attempts failed');
                    return;
                }

                const videoSrc = corsProxies[currentProxyIndex];
                console.log(`Trying proxy ${currentProxyIndex + 1}/${corsProxies.length}: ${videoSrc}`);
                statusElement.textContent = `Proxy ${currentProxyIndex + 1} diye try korche...`;

                if (hls) {
                    hls.destroy();
                }

                loadVideoWithHls(videoSrc);
            }

            // Custom loader to proxy all requests
            class ProxyLoader extends Hls.DefaultConfig.loader {
                load(context, config, callbacks) {
                    // Check if we're using the local proxy
                    if (currentProxyIndex === 0) {
                        // Proxy all requests through our local server
                        const originalUrl = context.url;
                        context.url = `http://localhost:3001/proxy?url=${encodeURIComponent(originalUrl)}`;
                        console.log(`Proxying: ${originalUrl} -> ${context.url}`);
                    }
                    super.load(context, config, callbacks);
                }
            }

            // Function to load video with HLS.js
            function loadVideoWithHls(videoSrc) {
                console.log("HLS.js is supported. Initializing player.");
                statusElement.textContent = 'Player load hocche...';

                hls = new Hls({
                    // Use custom loader for proxying
                    loader: ProxyLoader,
                    // Increase timeout and retry attempts for manifest loading
                    manifestLoadingTimeOut: 30000,
                    manifestLoadingMaxRetry: 10,
                    manifestLoadingRetryDelay: 2000,
                    // Fragment loading settings
                    fragLoadingTimeOut: 20000,
                    fragLoadingMaxRetry: 6,
                    fragLoadingRetryDelay: 1000,
                    // Level loading settings
                    levelLoadingTimeOut: 10000,
                    levelLoadingMaxRetry: 4,
                    levelLoadingRetryDelay: 1000,
                    // CORS and credential settings
                    xhrSetup: function(xhr, url) {
                        xhr.withCredentials = false;
                        // Don't add CORS headers when using proxy
                        if (!url.includes('localhost:3001')) {
                            xhr.setRequestHeader('Access-Control-Allow-Origin', '*');
                            xhr.setRequestHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
                            xhr.setRequestHeader('Access-Control-Allow-Headers', 'Content-Type');
                        }
                    },
                    // Enable debug mode for better error reporting
                    debug: true,
                    // Enable low latency mode
                    lowLatencyMode: true,
                    // Backbuffer length
                    backBufferLength: 90
                });

                // Load the video source
                hls.loadSource(videoSrc);
                // Attach HLS.js to the video element
                hls.attachMedia(video);

                // Event listener for when the manifest is parsed
                hls.on(Hls.Events.MANIFEST_PARSED, () => {
                    console.log('Manifest parsed. Playing video.');
                    statusElement.textContent = 'Video cholche.';
                    // Attempt to play the video. Muted autoplay is generally allowed.
                    video.play().catch(error => {
                        console.error('Autoplay was prevented:', error);
                        statusElement.textContent = 'Play button-e click korun.';
                    });
                });

                // Error handling
                hls.on(Hls.Events.ERROR, (event, data) => {
                    console.error("HLS.js error:", data); // Log the full error data
                    if (data.fatal) {
                        switch (data.type) {
                            case Hls.ErrorTypes.NETWORK_ERROR:
                                // This error is often due to CORS issues or network problems.
                                if (data.details === 'manifestLoadError') {
                                    console.error('Manifest load error. Trying next proxy...');
                                    currentProxyIndex++;
                                    setTimeout(tryLoadVideo, 1000); // Try next proxy after 1 second
                                } else {
                                    console.error('Fatal network error occurred, trying to recover:', data);
                                    statusElement.textContent = 'Network error. Punoray cheshta kora hocche...';
                                    hls.startLoad(); // try to recover
                                }
                                break;
                            case Hls.ErrorTypes.MEDIA_ERROR:
                                console.error('Fatal media error occurred, trying to recover:', data);
                                statusElement.textContent = 'Media error. Player recover korar cheshta korche...';
                                hls.recoverMediaError();
                                break;
                            default:
                                // Cannot recover
                                console.error('Fatal error, cannot recover:', data);
                                currentProxyIndex++;
                                setTimeout(tryLoadVideo, 1000); // Try next proxy
                                break;
                        }
                    }
                });
            }

            // Check if HLS is supported by the browser
            if (Hls.isSupported()) {
                tryLoadVideo(); // Start trying to load the video

            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                // For browsers like Safari that have native HLS support
                console.log("Native HLS support found.");

                function tryNativeHLS() {
                    if (currentProxyIndex >= corsProxies.length) {
                        statusElement.textContent = 'Shob proxy try kora hoyeche. Video load kora jayni.';
                        return;
                    }

                    const videoSrc = corsProxies[currentProxyIndex];
                    console.log(`Trying native HLS with proxy ${currentProxyIndex + 1}/${corsProxies.length}: ${videoSrc}`);
                    statusElement.textContent = `Native player diye proxy ${currentProxyIndex + 1} try korche...`;

                    video.src = videoSrc;
                    video.addEventListener('loadedmetadata', () => {
                        statusElement.textContent = 'Video cholche.';
                        video.play().catch(error => {
                            console.error('Autoplay was prevented:', error);
                            statusElement.textContent = 'Play button-e click korun.';
                        });
                    });

                    video.addEventListener('error', (e) => {
                        console.error('Native player error:', e);
                        currentProxyIndex++;
                        setTimeout(tryNativeHLS, 1000); // Try next proxy
                    });
                }

                tryNativeHLS();
            } else {
                // HLS is not supported
                console.error("Sorry, your browser does not support HLS video playback.");
                statusElement.textContent = 'Apnar browser HLS video support kore na.';
            }
        });
    </script>

</body>
</html>
