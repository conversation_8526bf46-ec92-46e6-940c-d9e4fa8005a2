<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Proxy Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>HLS Proxy Test</h1>
        <p>This page tests if the proxy server is working correctly.</p>
        
        <button onclick="testProxyServer()">Test Proxy Server</button>
        <button onclick="testManifest()">Test Manifest Loading</button>
        <button onclick="clearResults()">Clear Results</button>
        
        <div id="results"></div>
    </div>

    <script>
        const resultsDiv = document.getElementById('results');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }
        
        function clearResults() {
            resultsDiv.innerHTML = '';
        }
        
        async function testProxyServer() {
            addResult('Testing proxy server health...', 'info');
            
            try {
                const response = await fetch('http://localhost:3001/health');
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Proxy server is running: ${data.message}`, 'success');
                } else {
                    addResult(`❌ Proxy server responded with status: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Cannot connect to proxy server: ${error.message}`, 'error');
                addResult('Make sure to run: <code>npm start</code> to start the proxy server', 'info');
            }
        }
        
        async function testManifest() {
            addResult('Testing manifest loading through proxy...', 'info');
            
            const originalUrl = 'https://vid24.strp2p.com/hls/eZqh3HvcnrH9rrxAYr7Xlg/vz/b6ikxf9x/8v9bn3/tt/index-f1-v1-a1.m3u8?v=**********';
            const proxyUrl = `http://localhost:3001/proxy?url=${encodeURIComponent(originalUrl)}`;
            
            try {
                addResult(`Requesting: <pre>${proxyUrl}</pre>`, 'info');
                
                const response = await fetch(proxyUrl);
                
                if (response.ok) {
                    const text = await response.text();
                    addResult(`✅ Manifest loaded successfully (${text.length} bytes)`, 'success');
                    
                    // Show first few lines of manifest
                    const lines = text.split('\n').slice(0, 10);
                    addResult(`First 10 lines of manifest:<pre>${lines.join('\n')}</pre>`, 'info');
                    
                    // Check if it's a valid M3U8
                    if (text.includes('#EXTM3U')) {
                        addResult('✅ Valid M3U8 manifest detected', 'success');
                    } else {
                        addResult('⚠️ Response doesn\'t look like a valid M3U8 manifest', 'error');
                    }
                } else {
                    addResult(`❌ Failed to load manifest: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Error loading manifest: ${error.message}`, 'error');
            }
        }
        
        // Auto-test on page load
        window.addEventListener('load', () => {
            setTimeout(testProxyServer, 500);
        });
    </script>
</body>
</html>
