# HLS Video Player with CORS Fix

This project provides a custom HLS video player that handles CORS issues when accessing external video streams.

## Issues Fixed

1. **CORS Policy Error**: The external HLS stream doesn't allow cross-origin requests
2. **Missing Favicon**: Added a favicon to prevent 404 errors
3. **Network Error Handling**: Improved error handling and retry mechanisms

## Solutions Implemented

### 1. Multiple Proxy Options
The player now tries multiple proxy services in order:
- Local CORS proxy server (recommended)
- Direct access (fallback)
- Public CORS proxy services

### 2. Local CORS Proxy Server (Recommended Solution)

#### Setup Instructions:

1. **Install Node.js** (if not already installed):
   - Download from [nodejs.org](https://nodejs.org/)

2. **Install Dependencies**:
   ```bash
   npm install
   ```

3. **Start the Proxy Server**:
   ```bash
   npm start
   ```
   
   The proxy server will run on `http://localhost:3001`

4. **Test the Proxy** (Optional):
   - Open `test-proxy.html` in your browser to verify proxy is working
   - Click "Test Proxy Server" and "Test Manifest Loading"

5. **Open the HTML File**:
   - Open `index.html` in your browser
   - The player will automatically try the local proxy first

#### How the Local Proxy Works:
- **Dynamic Proxy**: Can handle requests to any domain (vid24.strp2p.com, tiktokcdn.com, etc.)
- **CORS Headers**: Adds proper CORS headers to all responses
- **Fragment Proxying**: Proxies both manifest and video fragment requests
- **Custom Loader**: Uses HLS.js custom loader to route all requests through proxy
- **Better Reliability**: More stable than public proxy services

### 3. Fallback Options
If the local proxy fails, the player automatically tries:
- Direct access to the original URL
- Public CORS proxy services (cors-anywhere, allorigins, corsproxy)

## Usage

### Option 1: With Local Proxy (Recommended)
1. Run `npm start` to start the proxy server
2. Open `index.html` in your browser
3. The video should load automatically

### Option 2: Without Local Proxy
1. Simply open `index.html` in your browser
2. The player will try direct access and public proxies
3. May have limited success due to CORS restrictions

## Features

- **Automatic Proxy Switching**: Tries multiple proxy options automatically
- **Enhanced Error Handling**: Better error messages and recovery
- **Improved Loading**: Increased timeouts and retry attempts
- **Debug Mode**: Console logging for troubleshooting
- **Responsive Design**: Works on desktop and mobile devices

## Troubleshooting

### If video still doesn't load:

1. **Check Console**: Open browser developer tools (F12) and check console for errors
2. **Try Different Browser**: Some browsers have stricter CORS policies
3. **Check Network**: Ensure you have internet connection
4. **Proxy Server**: Make sure the local proxy server is running (`npm start`)

### Common Error Messages:

- **"CORS policy"**: Use the local proxy server solution
- **"Network error"**: Check internet connection or try different proxy
- **"Manifest load error"**: The stream URL might be expired or invalid

## Technical Details

### CORS (Cross-Origin Resource Sharing)
The main issue was that the HLS stream server doesn't include the necessary CORS headers to allow browser access from different origins (like your local development server).

### Solution Approach
1. **Local Proxy**: Routes requests through a local server that adds CORS headers
2. **Multiple Fallbacks**: Tries different proxy services if one fails
3. **Enhanced Configuration**: Optimized HLS.js settings for better compatibility

## Files

- `index.html`: Main video player page with custom HLS loader
- `proxy-server.js`: Dynamic CORS proxy server (handles multiple domains)
- `test-proxy.html`: Proxy testing page
- `package.json`: Node.js dependencies
- `start-proxy.bat`: Windows batch file to start proxy
- `README.md`: This documentation

## Dependencies

- **express**: Web server framework
- **http-proxy-middleware**: Proxy middleware for Express
- **cors**: CORS middleware
- **HLS.js**: JavaScript HLS player library
- **Tailwind CSS**: Styling framework
